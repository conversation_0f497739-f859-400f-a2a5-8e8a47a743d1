<?php

/**
 * @file
 * Module file for Import Réglementation.
 */

use Drupal\Core\Routing\RouteMatchInterface;

/**
 * Implements hook_help().
 */
function import_reglementation_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.import_reglementation':
      $output = '';
      $output .= '<h3>' . t('Import Réglementation') . '</h3>';
      $output .= '<p>' . t('Ce module permet d\'importer des réglementations depuis un fichier CSV.') . '</p>';
      return $output;
  }
}