<?php

/**
 * Script simple d'importation RH
 *
 * Usage: vendor/bin/drush scr web/modules/custom/import_reglementation/import_rh_simple.php
 */

// Chemin vers votre fichier CSV
$csv_file = '/var/www/html/mtl/web/modules/custom/import_reglementation/csv/Réglementation_MTL - RH et textes généraux (1).csv';

echo "=== IMPORTATION RH SIMPLIFIÉE ===\n";
echo "Fichier: $csv_file\n";

if (!file_exists($csv_file)) {
  echo "❌ Erreur: Le fichier $csv_file n'existe pas.\n";
  return;
}

// Statistiques
$stats = [
  'processed' => 0,
  'created' => 0,
  'updated' => 0,
  'errors' => [],
];

// Ouvrir le fichier CSV
if (($handle = fopen($csv_file, 'r')) !== FALSE) {

  // Lire l'en-tête
  $header = fgetcsv($handle, 0, ',');
  echo "Colonnes détectées: " . implode(', ', $header) . "\n";

  // Vérifier les colonnes requises
  $required = ['Sous secteur', 'Type', 'Thème', 'N° du texte', 'Intitulé en Français', 'Date De publication'];
  $missing = array_diff($required, $header);

  if (!empty($missing)) {
    echo "❌ Colonnes manquantes: " . implode(', ', $missing) . "\n";
    fclose($handle);
    return;
  }

  echo "✅ Toutes les colonnes requises sont présentes.\n\n";

  // Traiter chaque ligne
  $line_number = 1;
  while (($data = fgetcsv($handle, 0, ',')) !== FALSE) {
    $line_number++;
    $stats['processed']++;

    // Créer un tableau associatif
    if (count($data) !== count($header)) {
      // Ajuster le nombre de colonnes
      if (count($data) < count($header)) {
        $data = array_pad($data, count($header), '');
      } else {
        $data = array_slice($data, 0, count($header));
      }
    }

    $row = array_combine($header, $data);

    // Vérifier les champs obligatoires
    if (empty($row['Intitulé en Français']) || empty($row['N° du texte'])) {
      $stats['errors'][] = "Ligne $line_number: titre français ou numéro de texte manquant";
      continue;
    }

    echo "Traitement ligne $line_number: " . $row['N° du texte'] . " - " . substr($row['Intitulé en Français'], 0, 50) . "...\n";

    try {
      // Vérifier si le nœud existe déjà
      $query = \Drupal::entityQuery('node')
        ->accessCheck(FALSE)
        ->condition('type', 'reglementation')
        ->condition('field_numero_de_text', $row['N° du texte']);

      $nids = $query->execute();

      if (!empty($nids)) {
        // Mise à jour
        $node = \Drupal\node\Entity\Node::load(reset($nids));
        $stats['updated']++;
        echo "  → Mise à jour du nœud existant ID: " . $node->id() . "\n";
      } else {
        // Création
        $node = \Drupal\node\Entity\Node::create(['type' => 'reglementation']);
        $stats['created']++;
        echo "  → Création d'un nouveau nœud\n";
      }

      // Mapper les champs de base
      $node->set('langcode', 'fr');

      // Titre
      $title = trim($row['Intitulé en Français']);
      if (empty($title) || $title === '-') {
        $title = $row['N° du texte'];
      }
      if (mb_strlen($title) > 255) {
        $title = mb_substr($title, 0, 252) . '...';
      }
      $node->setTitle($title);

      // Numéro de texte
      $node->set('field_numero_de_text', $row['N° du texte']);

      // Date de publication
      if (!empty($row['Date De publication'])) {
        $date_string = trim($row['Date De publication']);

        // Format DD/MM/YYYY
        if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $date_string, $matches)) {
          $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
          $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
          $year = $matches[3];
          $formatted_date = "{$year}-{$month}-{$day}";
          $node->set('field_date', $formatted_date);
          echo "  → Date: $formatted_date\n";
        }
      }

      // Sauvegarder
      $node->save();
      echo "  → Sauvegardé avec succès\n";

      // Ajouter traduction arabe si disponible
      if (!empty($row['Intitulé en Arabe'])) {
        try {
          if ($node->hasTranslation('ar')) {
            $translation = $node->getTranslation('ar');
          } else {
            $translation = $node->addTranslation('ar');
          }

          $title_ar = trim($row['Intitulé en Arabe']);
          if (empty($title_ar) || $title_ar === '-') {
            $title_ar = $row['N° du texte'];
          }
          if (mb_strlen($title_ar) > 255) {
            $title_ar = mb_substr($title_ar, 0, 252) . '...';
          }

          $translation->setTitle($title_ar);
          $translation->set('field_numero_de_text', $row['N° du texte']);

          if (!empty($formatted_date)) {
            $translation->set('field_date', $formatted_date);
          }

          $translation->save();
          echo "  → Traduction arabe ajoutée\n";

        } catch (\Exception $e) {
          echo "  → Erreur traduction arabe: " . $e->getMessage() . "\n";
        }
      }

    } catch (\Exception $e) {
      $stats['errors'][] = "Ligne $line_number: " . $e->getMessage();
      echo "  → ❌ Erreur: " . $e->getMessage() . "\n";
    }

    echo "\n";

    // Limiter pour éviter les timeouts (commenté pour importation complète)
    // if ($stats['processed'] >= 10) {
    //   echo "Limite de 10 lignes atteinte pour ce test.\n";
    //   break;
    // }
  }

  fclose($handle);

} else {
  echo "❌ Impossible d'ouvrir le fichier CSV.\n";
  return;
}

// Afficher les résultats
echo "\n=== RÉSULTATS ===\n";
echo "Lignes traitées: " . $stats['processed'] . "\n";
echo "Nœuds créés: " . $stats['created'] . "\n";
echo "Nœuds mis à jour: " . $stats['updated'] . "\n";
echo "Erreurs: " . count($stats['errors']) . "\n";

if (!empty($stats['errors'])) {
  echo "\n=== ERREURS ===\n";
  foreach ($stats['errors'] as $error) {
    echo "- $error\n";
  }
}

echo "\n✅ Script terminé.\n";
