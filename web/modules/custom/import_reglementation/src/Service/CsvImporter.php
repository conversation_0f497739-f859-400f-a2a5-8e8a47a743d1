<?php

namespace Drupal\import_reglementation\Service;

use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON><PERSON>\taxonomy\Entity\Term;
use Dr<PERSON>al\Core\Datetime\DrupalDateTime;
use Drupal\Core\StringTranslation\StringTranslationTrait;
use <PERSON><PERSON>al\Core\Logger\LoggerChannelFactoryInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Messenger\MessengerInterface;
use Drupal\Core\File\FileSystemInterface;

/**
 * Service pour importer des réglementations depuis un fichier CSV.
 */
class CsvImporter {
  use StringTranslationTrait;

  /**
   * The logger factory.
   *
   * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
   */
  protected $loggerFactory;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The messenger service.
   *
   * @var \Drupal\Core\Messenger\MessengerInterface
   */
  protected $messenger;

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * Cache for taxonomy terms to avoid repeated database queries.
   *
   * @var array
   */
  protected $termCache = [];

  /**
   * Maximum file size allowed (in bytes) - 10MB by default.
   *
   * @var int
   */
  protected $maxFileSize = 10485760;

  /**
   * Maximum number of rows to process in one batch.
   *
   * @var int
   */
  protected $maxRowsPerBatch = 100;

  /**
   * Allowed MIME types for CSV files.
   *
   * @var array
   */
  protected $allowedMimeTypes = [
    'text/csv',
    'text/plain',
    'application/csv',
    'application/vnd.ms-excel',
  ];

  /**
   * Constructs a new CsvImporter object.
   *
   * @param \Drupal\Core\Logger\LoggerChannelFactoryInterface $logger_factory
   *   The logger factory.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Messenger\MessengerInterface $messenger
   *   The messenger service.
   * @param \Drupal\Core\File\FileSystemInterface $file_system
   *   The file system service.
   */
  public function __construct(
    LoggerChannelFactoryInterface $logger_factory,
    EntityTypeManagerInterface $entity_type_manager,
    MessengerInterface $messenger,
    FileSystemInterface $file_system
  ) {
    $this->loggerFactory = $logger_factory;
    $this->entityTypeManager = $entity_type_manager;
    $this->messenger = $messenger;
    $this->fileSystem = $file_system;
  }

  /**
   * Validates a CSV file for security and format requirements.
   *
   * @param string $file_path
   *   Path to the CSV file.
   *
   * @return array
   *   Array with 'valid' boolean and 'errors' array.
   */
  protected function validateFile($file_path) {
    $validation = ['valid' => TRUE, 'errors' => []];

    // Check if file exists
    if (!file_exists($file_path)) {
      $validation['valid'] = FALSE;
      $validation['errors'][] = $this->t('Le fichier CSV n\'existe pas.');
      return $validation;
    }

    // Check file size
    $file_size = filesize($file_path);
    if ($file_size === FALSE) {
      $validation['valid'] = FALSE;
      $validation['errors'][] = $this->t('Impossible de déterminer la taille du fichier.');
      return $validation;
    }

    if ($file_size > $this->maxFileSize) {
      $validation['valid'] = FALSE;
      $validation['errors'][] = $this->t('Le fichier est trop volumineux. Taille maximale autorisée : @max MB.', [
        '@max' => round($this->maxFileSize / 1048576, 2),
      ]);
      return $validation;
    }

    // Check MIME type
    $mime_type = mime_content_type($file_path);
    if (!in_array($mime_type, $this->allowedMimeTypes)) {
      $validation['valid'] = FALSE;
      $validation['errors'][] = $this->t('Type de fichier non autorisé. Types acceptés : @types', [
        '@types' => implode(', ', $this->allowedMimeTypes),
      ]);
      return $validation;
    }

    // Check file permissions
    if (!is_readable($file_path)) {
      $validation['valid'] = FALSE;
      $validation['errors'][] = $this->t('Le fichier n\'est pas lisible.');
      return $validation;
    }

    return $validation;
  }

  /**
   * Sanitizes input data to prevent security issues.
   *
   * @param string $input
   *   The input string to sanitize.
   *
   * @return string
   *   The sanitized string.
   */
  protected function sanitizeInput($input) {
    if (!is_string($input)) {
      return '';
    }

    // Remove null bytes and control characters except newlines and tabs
    $input = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $input);

    // Trim whitespace
    $input = trim($input);

    // Limit length to prevent memory issues
    if (mb_strlen($input) > 1000) {
      $input = mb_substr($input, 0, 1000);
    }

    return $input;
  }

  /**
   * Prépare les données pour l'importation CSV.
   *
   * @param string $file_path
   *   Chemin vers le fichier CSV.
   * @param string $delimiter
   *   Délimiteur utilisé dans le CSV.
   *
   * @return array
   *   Tableau contenant les données préparées et les informations contextuelles.
   */
  public function prepareImportData($file_path, $delimiter = ',') {
    $rows = [];
    $validation_data = [
      'file_path' => $file_path,
      'delimiter' => $delimiter,
      'results' => [
        'success' => FALSE,
        'created' => 0,
        'updated' => 0,
        'errors' => [],
      ],
    ];

    // Validate file security and format
    $file_validation = $this->validateFile($file_path);
    if (!$file_validation['valid']) {
      $validation_data['results']['errors'] = array_merge(
        $validation_data['results']['errors'],
        $file_validation['errors']
      );
      return ['rows' => $rows, 'validation_data' => $validation_data];
    }

    if (($handle = fopen($file_path, 'r')) !== FALSE) {
      // Lire l'en-tête pour obtenir les noms de colonnes.
      $header = fgetcsv($handle, 0, $delimiter);

      // Sanitize header columns
      if ($header) {
        $header = array_map([$this, 'sanitizeInput'], $header);
      }

      // Vérifier que les colonnes requises sont présentes
      // Support pour les deux formats de CSV
      $required_columns_format1 = ['Secteur', 'Thème', 'N° du texte', 'Type', 'Intitulé en français', 'Date de Publication'];
      $required_columns_format2 = ['Sous secteur', 'Thème', 'N° du texte', 'Type', 'Intitulé en Français', 'Date De publication'];

      // Déterminer quel format utiliser
      $format1_missing = [];
      $format2_missing = [];

      foreach ($required_columns_format1 as $column) {
        if (!in_array($column, $header)) {
          $format1_missing[] = $column;
        }
      }

      foreach ($required_columns_format2 as $column) {
        if (!in_array($column, $header)) {
          $format2_missing[] = $column;
        }
      }

      // Choisir le format avec le moins de colonnes manquantes
      if (count($format1_missing) <= count($format2_missing)) {
        $required_columns = $required_columns_format1;
        $missing_columns = $format1_missing;
      } else {
        $required_columns = $required_columns_format2;
        $missing_columns = $format2_missing;
      }

      // Si des colonnes sont manquantes, signaler l'erreur
      if (!empty($missing_columns)) {
        foreach ($missing_columns as $column) {
          $validation_data['results']['errors'][] = $this->t('La colonne requise @column est manquante dans le fichier CSV.', ['@column' => $column]);
        }
        fclose($handle);
        return ['rows' => $rows, 'validation_data' => $validation_data];
      }

      // Lire toutes les lignes du CSV avec limitation pour éviter les attaques DoS
      $line_number = 0;
      $max_lines = 10000; // Limite de sécurité

      while (($data = fgetcsv($handle, 0, $delimiter)) !== FALSE && $line_number < $max_lines) {
        $line_number++;

        // Sanitize data
        $data = array_map([$this, 'sanitizeInput'], $data);

        if (count($data) === count($header)) {
          $rows[] = array_combine($header, $data);
        }
        else {
          $validation_data['results']['errors'][] = $this->t('La ligne @line_number du fichier CSV contient un nombre incorrect de colonnes.', ['@line_number' => $line_number]);
        }
      }

      // Check if we hit the line limit
      if ($line_number >= $max_lines) {
        $validation_data['results']['errors'][] = $this->t('Le fichier contient trop de lignes. Maximum autorisé : @max', ['@max' => $max_lines]);
      }

      fclose($handle);

      $validation_data['total_rows'] = count($rows);
      $validation_data['results']['success'] = TRUE;
    }
    else {
      $validation_data['results']['errors'][] = $this->t('Impossible d\'ouvrir le fichier CSV.');
    }

    return ['rows' => $rows, 'validation_data' => $validation_data];
  }

  /**
   * Traite un ensemble de lignes CSV.
   *
   * @param array $rows
   *   Les lignes CSV à traiter.
   * @param array $results
   *   Tableau des résultats passé par référence.
   */
  public static function processRowsData(array $rows, &$results) {
    // Initialiser les résultats si nécessaire
    if (!isset($results['created'])) {
      $results['created'] = 0;
      $results['updated'] = 0;
      $results['errors'] = [];
      $results['success'] = TRUE;
    }

    $importer = \Drupal::service('import_reglementation.csv_importer');
    $logger = \Drupal::logger('import_reglementation');

    // Traiter chaque ligne avec limitation de batch pour éviter les timeouts
    $processed = 0;
    $max_batch_size = 50; // Limite pour éviter les timeouts

    foreach ($rows as $index => $row) {
      try {
        // Vérifier la limite de batch
        if ($processed >= $max_batch_size) {
          $logger->notice('Limite de batch atteinte (@limit lignes), arrêt du traitement.', [
            '@limit' => $max_batch_size,
          ]);
          break;
        }

        // S'assurer que la ligne est valide
        if (!is_array($row) || !isset($row['N° du texte']) || !isset($row['Intitulé en français'])) {
          $results['errors'][] = t('Ligne @index invalide: données manquantes', ['@index' => $index + 1]);
          continue;
        }

        // Sanitize row data
        $row = array_map(function($value) {
          return is_string($value) ? trim($value) : $value;
        }, $row);

        // Vérifier si une réglementation avec ce numéro existe déjà
        $query = \Drupal::entityQuery('node')
          ->accessCheck(FALSE)
          ->condition('type', 'reglementation')
          ->condition('field_numero_de_text', $row['N° du texte'])
          ->range(0, 1); // Optimisation: limiter à 1 résultat

        $nids = $query->execute();

        if (!empty($nids)) {
          // Mise à jour de l'existant
          $node = Node::load(reset($nids));
          if ($node) {
            $results['updated']++;
          } else {
            $results['errors'][] = t('Impossible de charger le nœud existant pour le texte @numero', [
              '@numero' => $row['N° du texte'],
            ]);
            continue;
          }
        }
        else {
          // Création d'une nouvelle réglementation
          $node = Node::create(['type' => 'reglementation']);
          $results['created']++;
        }

        // Mapper les champs du CSV vers les champs Drupal
        $importer->mapFields($node, $row);
        $processed++;
      }
      catch (\Exception $e) {
        $results['errors'][] = t('Erreur lors du traitement de la ligne @index: @error', [
          '@index' => $index + 1,
          '@error' => $e->getMessage(),
        ]);
        $logger->error('Erreur lors du traitement de la ligne @index: @error', [
          '@index' => $index + 1,
          '@error' => $e->getMessage(),
        ]);
      }
    }
  }

  /**
   * Fonction de finalisation du traitement d'importation.
   *
   * @param bool $success
   *   Indique si le traitement a réussi.
   * @param array $results
   *   Résultats du traitement.
   */
  public static function importFinished($success, $results) {
    // S'assurer que les résultats sont valides
    if (!is_array($results)) {
      $results = [
        'created' => 0,
        'updated' => 0,
        'errors' => [],
      ];
    }

    // Initialiser les compteurs s'ils n'existent pas
    if (!isset($results['created'])) {
      $results['created'] = 0;
    }
    if (!isset($results['updated'])) {
      $results['updated'] = 0;
    }
    if (!isset($results['errors'])) {
      $results['errors'] = [];
    }

    if ($success) {
      $message = \Drupal::translation()->formatPlural(
        (int) $results['created'] + (int) $results['updated'],
        'Une réglementation a été importée.',
        '@count réglementations ont été importées.'
      );
      \Drupal::messenger()->addStatus($message);
      \Drupal::messenger()->addStatus(t('@created réglementations créées, @updated mises à jour.', [
        '@created' => $results['created'],
        '@updated' => $results['updated'],
      ]));
    }
    else {
      \Drupal::messenger()->addError(t('Une erreur est survenue lors de l\'import.'));
    }

    if (!empty($results['errors'])) {
      foreach ($results['errors'] as $error) {
        if (is_string($error)) {
          \Drupal::messenger()->addError($error);
        }
        else {
          \Drupal::messenger()->addError(t('Une erreur inconnue est survenue.'));
        }
      }
    }
  }

  /**
   * Importe les données depuis un fichier CSV.
   *
   * @param string $file_path
   *   Chemin vers le fichier CSV.
   * @param string $delimiter
   *   Délimiteur utilisé dans le CSV.
   *
   * @return array
   *   Tableau contenant les résultats de l'import.
   */
  public function import($file_path, $delimiter = ',') {
    $results = [
      'success' => FALSE,
      'created' => 0,
      'updated' => 0,
      'errors' => [],
      'processed' => 0,
    ];

    $logger = $this->loggerFactory->get('import_reglementation');

    // Journaliser le début de l'importation (sans exposer le chemin complet pour la sécurité)
    $logger->notice('Début de l\'importation du fichier CSV');

    // Validate file security and format
    $file_validation = $this->validateFile($file_path);
    if (!$file_validation['valid']) {
      $results['errors'] = array_merge($results['errors'], $file_validation['errors']);
      $logger->error('Validation du fichier échouée');
      return $results;
    }

    $logger->notice('Fichier CSV validé avec succès');

    if (($handle = fopen($file_path, 'r')) !== FALSE) {
      // Lire l'en-tête pour obtenir les noms de colonnes.
      $header = fgetcsv($handle, 0, $delimiter);

      // Sanitize header columns
      if ($header) {
        $header = array_map([$this, 'sanitizeInput'], $header);
      }

      // Vérifier que les colonnes requises sont présentes
      // Support pour les deux formats de CSV
      $required_columns_format1 = ['Secteur', 'Thème', 'N° du texte', 'Type', 'Intitulé en français', 'Date de Publication'];
      $required_columns_format2 = ['Sous secteur', 'Thème', 'N° du texte', 'Type', 'Intitulé en Français', 'Date De publication'];

      // Déterminer quel format utiliser
      $format1_missing = [];
      $format2_missing = [];

      foreach ($required_columns_format1 as $column) {
        if (!in_array($column, $header)) {
          $format1_missing[] = $column;
        }
      }

      foreach ($required_columns_format2 as $column) {
        if (!in_array($column, $header)) {
          $format2_missing[] = $column;
        }
      }

      // Choisir le format avec le moins de colonnes manquantes
      if (count($format1_missing) <= count($format2_missing)) {
        $missing_columns = $format1_missing;
      } else {
        $missing_columns = $format2_missing;
      }

      // Si des colonnes sont manquantes, signaler l'erreur
      if (!empty($missing_columns)) {
        foreach ($missing_columns as $column) {
          $results['errors'][] = $this->t('La colonne requise @column est manquante dans le fichier CSV.', ['@column' => $column]);
        }
        fclose($handle);
        return $results;
      }

      // Traiter chaque ligne du CSV avec limitations de sécurité
      $line_number = 1; // Commencer à 1 pour la ligne d'en-tête
      $max_lines = 10000; // Limite de sécurité

      while (($data = fgetcsv($handle, 0, $delimiter)) !== FALSE && $line_number < $max_lines) {
        $line_number++;
        $results['processed']++;

        // Sanitize data
        $data = array_map([$this, 'sanitizeInput'], $data);

        // Vérifier que le nombre de colonnes correspond
        if (count($data) !== count($header)) {
          $logger->warning('La ligne @line a @actual colonnes au lieu de @expected attendues', [
            '@line' => $line_number,
            '@actual' => count($data),
            '@expected' => count($header),
          ]);

          // Ajuster les données si nécessaire
          if (count($data) < count($header)) {
            $data = array_pad($data, count($header), '');
          } else if (count($data) > count($header)) {
            $data = array_slice($data, 0, count($header));
          }
        }

        $row = array_combine($header, $data);

        // Journaliser la ligne en cours de traitement
        \Drupal::logger('import_reglementation')->notice('Traitement de la ligne @line: N° du texte = @numero, Titre = @titre', [
          '@line' => $line_number,
          '@numero' => $row['N° du texte'] ?? 'N/A',
          '@titre' => $row['Intitulé en français'] ?? 'N/A',
        ]);

        // Vérifier que les champs obligatoires sont présents
        // Support pour les deux formats de titre français
        $title_fr = $row['Intitulé en français'] ?? $row['Intitulé en Français'] ?? '';
        if (empty($title_fr)) {
          \Drupal::logger('import_reglementation')->warning('La ligne @line n\'a pas de titre en français', [
            '@line' => $line_number,
          ]);
          $results['errors'][] = $this->t('La ligne @line n\'a pas de titre en français', ['@line' => $line_number]);
          continue;
        }

        try {
          // Vérifier si une réglementation avec ce numéro existe déjà
          $query = \Drupal::entityQuery('node')
            ->accessCheck(FALSE)
            ->condition('type', 'reglementation')
            ->condition('field_numero_de_text', $row['N° du texte']);

          $nids = $query->execute();

          if (!empty($nids)) {
            // Mise à jour de l'existant
            $node = Node::load(reset($nids));
            $results['updated']++;
          }
          else {
            // Création d'une nouvelle réglementation
            $node = Node::create(['type' => 'reglementation']);
            $results['created']++;
          }

          // Mapper les champs du CSV vers les champs Drupal
          $this->mapFields($node, $row);
        }
        catch (\Exception $e) {
          $results['errors'][] = $this->t('Erreur lors du traitement de la ligne: @error', ['@error' => $e->getMessage()]);
          continue;
        }
      }

      fclose($handle);
      $results['success'] = TRUE;

      // Journaliser la fin de l'importation
      \Drupal::logger('import_reglementation')->notice('Fin de l\'importation. Résultats: @created créés, @updated mis à jour, @processed traités, @errors erreurs', [
        '@created' => $results['created'],
        '@updated' => $results['updated'],
        '@processed' => $results['processed'],
        '@errors' => count($results['errors']),
      ]);

      // Journaliser les erreurs spécifiques
      if (!empty($results['errors'])) {
        foreach ($results['errors'] as $index => $error) {
          \Drupal::logger('import_reglementation')->error('Erreur @index: @error', [
            '@index' => $index + 1,
            '@error' => $error,
          ]);
        }
      }

      // Vérifier si des éléments ont été créés ou mis à jour
      if ($results['created'] == 0 && $results['updated'] == 0) {
        \Drupal::logger('import_reglementation')->warning('Aucun élément n\'a été créé ou mis à jour malgré @processed lignes traitées.', [
          '@processed' => $results['processed'],
        ]);
      }
    } else {
      // Journaliser l'erreur d'ouverture du fichier
      \Drupal::logger('import_reglementation')->error('Impossible d\'ouvrir le fichier CSV: @file', [
        '@file' => $file_path,
      ]);
    }

    return $results;
  }

  /**
   * Mappe les données du CSV vers les champs du nœud.
   *
   * @param \Drupal\node\Entity\Node $node
   *   Le nœud à mettre à jour.
   * @param array $row
   *   Les données de la ligne CSV.
   */
  protected function mapFields(Node $node, array $row) {
    // Journaliser le début du mapping des champs
    \Drupal::logger('import_reglementation')->notice('Début du mapping des champs pour le texte @numero', [
      '@numero' => $row['N° du texte'] ?? 'N/A',
    ]);

    // Définir la langue par défaut (français)
    $node->set('langcode', 'fr');
    \Drupal::logger('import_reglementation')->notice('Langue définie: fr');

    // Titre en français (support pour les deux formats de CSV)
    $title_fr = $row['Intitulé en français'] ?? $row['Intitulé en Français'] ?? '';

    // Si l'intitulé est vide, "-" ou null, utiliser le numéro de texte
    if (empty($title_fr) || $title_fr === '-' || trim($title_fr) === '') {
      $title_fr = $row['N° du texte'];
      \Drupal::logger('import_reglementation')->notice('Intitulé français vide ou "-", utilisation du numéro de texte: @numero', [
        '@numero' => $title_fr,
      ]);
    }

    \Drupal::logger('import_reglementation')->notice('Titre français original: @title', [
      '@title' => $title_fr,
    ]);

    if (mb_strlen($title_fr) > 255) {
      $title_fr = mb_substr($title_fr, 0, 252) . '...';
      \Drupal::logger('import_reglementation')->notice('Titre français tronqué pour le texte @numero: @title', [
        '@numero' => $row['N° du texte'],
        '@title' => $title_fr,
      ]);
    }
    $node->setTitle($title_fr);
    \Drupal::logger('import_reglementation')->notice('Titre français défini: @title', [
      '@title' => $title_fr,
    ]);

    // Numéro de texte
    $node->set('field_numero_de_text', $row['N° du texte']);

    // Type de loi (taxonomie)
    if (!empty($row['Type'])) {
      $type_ar = !empty($row['AR']) ? $row['AR'] : '';
      $type_tid = $this->getOrCreateTerm('type', $row['Type'], $type_ar);
      if ($type_tid !== null) {
        $node->set('field_type_loi', $type_tid);
      }
    }

    // Secteurs (taxonomie)
    if (!empty($row['Secteur'])) {
      $secteur_ar = !empty($row['القطاع']) ? $row['القطاع'] : '';
      $secteur_tid = $this->getOrCreateTerm('modes_de_transport', $row['Secteur'], $secteur_ar);
      if ($secteur_tid !== null) {
        $node->set('field_secteur', $secteur_tid);
      }
    }

    // Thème (taxonomie field_domaine_d_activite)
    if (!empty($row['Thème'])) {
      $theme_ar = !empty($row['المحور']) ? $row['المحور'] : '';
      $theme_tid = $this->getOrCreateTerm('domaines_d_activites', $row['Thème'], $theme_ar);
      if ($theme_tid !== null) {
        $node->set('field_domaine_d_activite', $theme_tid);
      }
    }

    // Date de publication
    if (!empty($row['Date de Publication'])) {
      // Essayer différents formats de date
      $date_string = trim($row['Date de Publication']);

      \Drupal::logger('import_reglementation')->notice('Traitement de la date: @date', [
        '@date' => $date_string,
      ]);

      // Approche simplifiée pour les dates au format français spécifique
      // Gérer les formats comme "27-juil.-72", "25-fév-94", "22-jui-01", etc.
      if (preg_match('/^(\d{1,2})-([\wéûù]+)\.?-(\d{2})$/', $date_string, $matches) ||
          preg_match('/^(\d{1,2})-([\wéûù]+)-(\d{2})$/', $date_string, $matches)) {
        $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
        $month_text = strtolower($matches[2]);
        $year = $matches[3];

        \Drupal::logger('import_reglementation')->notice('Décomposition de la date: jour=@jour, mois=@mois, année=@annee', [
          '@jour' => $day,
          '@mois' => $month_text,
          '@annee' => $year,
        ]);

        // Mapping des mois en français vers leur numéro
        $month_mapping = [
          'janv' => '01', 'jan' => '01', 'janvier' => '01',
          'févr' => '02', 'fév' => '02', 'février' => '02', 'fevr' => '02', 'fev' => '02',
          'mars' => '03', 'mar' => '03',
          'avr' => '04', 'avril' => '04',
          'mai' => '05',
          'juin' => '06', 'jui' => '06', // Considérer "jui" comme juin par défaut
          'juil' => '07', 'juillet' => '07', 'jul' => '07',
          'août' => '08', 'aout' => '08', 'aoû' => '08', 'aou' => '08',
          'sept' => '09', 'sep' => '09', 'septembre' => '09',
          'oct' => '10', 'octobre' => '10',
          'nov' => '11', 'novembre' => '11',
          'déc' => '12', 'dec' => '12', 'décembre' => '12', 'decembre' => '12'
        ];

        // Cas spécial pour "jui" qui peut être juin ou juillet
        // Si le texte contient "jui" mais pas "juil", vérifier le contexte
        if ($month_text === 'jui') {
          \Drupal::logger('import_reglementation')->notice('Abréviation ambiguë "jui" détectée, considérée comme juin (06)', []);
          // On pourrait ajouter une logique plus sophistiquée ici si nécessaire
        }

        // Trouver le numéro du mois
        $month = '01'; // Valeur par défaut
        foreach ($month_mapping as $abbr => $num) {
          if (strpos($month_text, $abbr) === 0) {
            $month = $num;
            \Drupal::logger('import_reglementation')->notice('Mois trouvé: @abbr -> @num', [
              '@abbr' => $abbr,
              '@num' => $num,
            ]);
            break;
          }
        }

        // Déterminer l'année complète
        $year_full = ($year > 50) ? "19{$year}" : "20{$year}";

        // Créer la date au format Y-m-d
        $formatted_date = "{$year_full}-{$month}-{$day}";
        \Drupal::logger('import_reglementation')->notice('Date formatée: @formatted', [
          '@formatted' => $formatted_date,
        ]);

        // Créer un objet DrupalDateTime pour éviter les erreurs de validation
        try {
          $date_object = DrupalDateTime::createFromFormat('Y-m-d', $formatted_date);
          if ($date_object && !array_sum($date_object->getErrors())) {
            $node->set('field_date', $date_object->format('Y-m-d'));
            \Drupal::logger('import_reglementation')->notice('Date définie pour le nœud: @date', [
              '@date' => $formatted_date,
            ]);
          } else {
            \Drupal::logger('import_reglementation')->warning('Date invalide après formatage: @date', [
              '@date' => $formatted_date,
            ]);
          }
        } catch (\Exception $e) {
          \Drupal::logger('import_reglementation')->error('Erreur lors de la création de l\'objet date: @error', [
            '@error' => $e->getMessage(),
          ]);
        }
      } else {
        // Formats standards à essayer
        $formats = ['d.m.Y', 'd/m/Y', 'Y-m-d', 'd-m-Y'];

        $date_set = false;
        foreach ($formats as $format) {
          $date_attempt = DrupalDateTime::createFromFormat($format, $date_string);
          if ($date_attempt && !array_sum($date_attempt->getErrors())) {
            $formatted_date = $date_attempt->format('Y-m-d');
            $node->set('field_date', $formatted_date);
            \Drupal::logger('import_reglementation')->notice('Date standard reconnue: @original -> @formatted', [
              '@original' => $date_string,
              '@formatted' => $formatted_date,
            ]);
            $date_set = true;
            break;
          }
        }

        // Si aucun format n'a fonctionné, essayer une approche de dernier recours
        if (!$date_set) {
          \Drupal::logger('import_reglementation')->warning('Format de date non reconnu: @date pour le texte @numero, tentative de dernier recours', [
            '@date' => $date_string,
            '@numero' => $row['N° du texte'],
          ]);

          // Essayer de décomposer manuellement la date
          $parts = preg_split('/[-\/\.\s]/', $date_string);
          if (count($parts) >= 3) {
            $day = str_pad($parts[0], 2, '0', STR_PAD_LEFT);
            $month = str_pad($parts[1], 2, '0', STR_PAD_LEFT);
            $year = $parts[2];

            // Vérifier si l'année est sur 2 chiffres
            if (strlen($year) == 2) {
              $year = ($year > 50) ? "19{$year}" : "20{$year}";
            }

            // Créer la date au format Y-m-d
            $formatted_date = "{$year}-{$month}-{$day}";
            \Drupal::logger('import_reglementation')->notice('Date décomposée manuellement: @original -> @formatted', [
              '@original' => $date_string,
              '@formatted' => $formatted_date,
            ]);

            // Définir la date sans validation
            $node->set('field_date', $formatted_date);
          } else {
            // En dernier recours, utiliser la date actuelle
            $formatted_date = date('Y-m-d');
            \Drupal::logger('import_reglementation')->error('Impossible de parser la date: @date, utilisation de la date actuelle: @today', [
              '@date' => $date_string,
              '@today' => $formatted_date,
            ]);

            // Définir la date actuelle
            $node->set('field_date', $formatted_date);
          }
        }
      }
    }

    // Journaliser avant la sauvegarde
    \Drupal::logger('import_reglementation')->notice('Tentative de sauvegarde du nœud pour le texte @numero', [
      '@numero' => $row['N° du texte'] ?? 'N/A',
    ]);

    try {
      // Sauvegarder le nœud pour obtenir un ID
      $node->save();

      // Journaliser après la sauvegarde
      \Drupal::logger('import_reglementation')->notice('Nœud sauvegardé avec succès, ID: @id', [
        '@id' => $node->id(),
      ]);
    } catch (\Exception $e) {
      \Drupal::logger('import_reglementation')->error('Erreur lors de la sauvegarde du nœud: @error', [
        '@error' => $e->getMessage(),
      ]);
      throw $e; // Relancer l'exception pour qu'elle soit gérée par le code appelant
    }

    // Ajouter la traduction en arabe (toujours créer la traduction, même si l'intitulé est "-")
    $this->addArabicTranslation($node, $row);
  }

  /**
   * Ajoute une traduction en arabe pour un nœud.
   *
   * @param \Drupal\node\Entity\Node $node
   *   Le nœud à traduire.
   * @param array $row
   *   Les données de la ligne CSV.
   */
  protected function addArabicTranslation(Node $node, array $row) {
    try {
      // Vérifier si le nœud a déjà une traduction en arabe
      if ($node->hasTranslation('ar')) {
        $translation = $node->getTranslation('ar');
        \Drupal::logger('import_reglementation')->notice('Mise à jour de la traduction arabe existante pour le nœud @id', [
          '@id' => $node->id(),
        ]);
      }
      else {
        // Créer une nouvelle traduction
        $translation = $node->addTranslation('ar');
        \Drupal::logger('import_reglementation')->notice('Création d\'une nouvelle traduction arabe pour le nœud @id', [
          '@id' => $node->id(),
        ]);
      }

      // Titre en arabe (utiliser l'intitulé arabe ou le numéro de texte si intitulé est "-")
      $title_ar = $row['Intitulé en arabe'];

      // Si l'intitulé arabe est vide, "-" ou null, utiliser le numéro de texte
      if (empty($title_ar) || $title_ar === '-' || trim($title_ar) === '') {
        $title_ar = $row['N° du texte'];
        \Drupal::logger('import_reglementation')->notice('Intitulé arabe vide ou "-", utilisation du numéro de texte: @numero', [
          '@numero' => $title_ar,
        ]);
      }

      \Drupal::logger('import_reglementation')->notice('Titre arabe original: @title', [
        '@title' => $title_ar,
      ]);

      if (mb_strlen($title_ar) > 255) {
        $title_ar = mb_substr($title_ar, 0, 252) . '...';
        \Drupal::logger('import_reglementation')->notice('Titre arabe tronqué pour le texte @numero: @title', [
          '@numero' => $row['N° du texte'],
          '@title' => $title_ar,
        ]);
      }
      $translation->setTitle($title_ar);

    // Numéro de texte (même valeur que la version française)
    $translation->set('field_numero_de_text', $row['N° du texte']);

    // Type de loi (même valeur que la version française)
    if (!empty($row['Type']) && !empty($row['AR'])) {
      // On pourrait créer un terme arabe, mais pour simplifier on garde le même terme
      $type_ar = !empty($row['AR']) ? $row['AR'] : '';
      $type_tid = $this->getOrCreateTerm('type', $row['Type'], $type_ar);
      if ($type_tid !== null) {
        $translation->set('field_type_loi', $type_tid);
      }
    }

    // Secteurs (même valeur que la version française)
    if (!empty($row['Secteur']) && !empty($row['القطاع'])) {
      $secteur_ar = !empty($row['القطاع']) ? $row['القطاع'] : '';
      $secteur_tid = $this->getOrCreateTerm('modes_de_transport', $row['Secteur'], $secteur_ar);
      if ($secteur_tid !== null) {
        $translation->set('field_secteur', $secteur_tid);
      }
    }

    // Thème en arabe (taxonomie field_domaine_d_activite)
    if (!empty($row['المحور']) && !empty($row['Thème'])) {
      // On utilise le même terme que pour la version française
      // car les termes de taxonomie ne sont pas traduits automatiquement
      $theme_ar = !empty($row['المحور']) ? $row['المحور'] : '';
      $theme_tid = $this->getOrCreateTerm('domaines_d_activites', $row['Thème'], $theme_ar);
      if ($theme_tid !== null) {
        $translation->set('field_domaine_d_activite', $theme_tid);
      }
    }

    // Date de publication (même valeur que la version française)
    if (!empty($row['Date de Publication'])) {
      // Essayer différents formats de date
      $date_string = trim($row['Date de Publication']);
      $date = null;

      // Formats possibles à essayer
      $formats = ['d.m.Y', 'd/m/Y', 'Y-m-d', 'd-m-Y', 'd-M-y'];

      // Gérer les formats spécifiques comme "27-juil.-72", "25-fév-94" ou "02-août-11"
      if (preg_match('/^\d{1,2}-[a-zéûù]+\.?-\d{2}$/', $date_string)) {
        // Convertir les mois abrégés en français vers leur numéro
        $months_fr = [
          'janv' => '01', 'jan' => '01',
          'févr' => '02', 'fév' => '02',
          'mars' => '03', 'mar' => '03',
          'avr' => '04', 'avri' => '04',
          'mai' => '05',
          'juin' => '06',
          'juil' => '07',
          'août' => '08', 'aou' => '08',
          'sept' => '09', 'sep' => '09',
          'oct' => '10',
          'nov' => '11',
          'déc' => '12', 'dec' => '12'
        ];

        // Extraire jour, mois et année (avec ou sans point après l'abréviation)
        if (preg_match('/^(\d{1,2})-([a-zéûù]+)\.?-(\d{2})$/', $date_string, $matches)) {
          \Drupal::logger('import_reglementation')->notice('Correspondance trouvée pour la date (traduction arabe): @date', [
            '@date' => $date_string,
          ]);
          $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
          $month_text = strtolower(substr($matches[2], 0, 4));
          $year = $matches[3];

          // Déterminer le mois
          $month = '01'; // Par défaut
          foreach ($months_fr as $abbr => $num) {
            if (strpos($month_text, $abbr) === 0) {
              $month = $num;
              break;
            }
          }

          // Construire une date au format Y-m-d
          $year_full = ($year > 50) ? "19{$year}" : "20{$year}";
          $formatted_date = "{$year_full}-{$month}-{$day}";

          $date = DrupalDateTime::createFromFormat('Y-m-d', $formatted_date);
          if ($date && !array_sum($date->getErrors())) {
            // Si la date est valide, on peut sortir de la boucle
            \Drupal::logger('import_reglementation')->notice('Date convertie (traduction arabe): @original -> @formatted', [
              '@original' => $date_string,
              '@formatted' => $formatted_date,
            ]);
          } else {
            $date = null;
          }
        }
      }

      foreach ($formats as $format) {
        $date_attempt = DrupalDateTime::createFromFormat($format, $date_string);
        if ($date_attempt && !array_sum($date_attempt->getErrors())) {
          $date = $date_attempt;
          break;
        }
      }

      if ($date) {
        $translation->set('field_date', $date->format('Y-m-d'));
      } else {
        \Drupal::logger('import_reglementation')->warning('Format de date non reconnu (traduction arabe): @date pour le texte @numero', [
          '@date' => $date_string,
          '@numero' => $row['N° du texte'],
        ]);
      }
    }

      // Sauvegarder la traduction
      $translation->save();

      \Drupal::logger('import_reglementation')->notice('Traduction arabe sauvegardée avec succès pour le nœud @id', [
        '@id' => $node->id(),
      ]);

    } catch (\Exception $e) {
      \Drupal::logger('import_reglementation')->error('Erreur lors de l\'ajout de la traduction arabe pour le nœud @id: @error', [
        '@id' => $node->id(),
        '@error' => $e->getMessage(),
      ]);
    }
  }

  /**
   * Récupère ou crée un terme de taxonomie avec mise en cache.
   *
   * @param string $vocabulary
   *   Le vocabulaire cible.
   * @param string $name
   *   Le nom du terme en français.
   * @param string $name_ar
   *   Le nom du terme en arabe (optionnel).
   *
   * @return int|null
   *   L'ID du terme ou null en cas d'erreur.
   */
  protected function getOrCreateTerm($vocabulary, $name, $name_ar = '') {
    // Sanitize inputs
    $name = $this->sanitizeInput($name);
    $name_ar = $this->sanitizeInput($name_ar);
    $vocabulary = $this->sanitizeInput($vocabulary);

    if (empty($name) || empty($vocabulary)) {
      return null;
    }

    // Check cache first
    $cache_key = $vocabulary . ':' . $name;
    if (isset($this->termCache[$cache_key])) {
      return $this->termCache[$cache_key];
    }

    try {
      $logger = $this->loggerFactory->get('import_reglementation');

      // Vérifier si le vocabulaire existe
      $vocabularies = $this->entityTypeManager
        ->getStorage('taxonomy_vocabulary')
        ->loadByProperties(['vid' => $vocabulary]);

      if (empty($vocabularies)) {
        // Vérifier si nous pouvons mapper ce vocabulaire à un existant
        $vocabulary_mapping = [
          'type' => 'type',
          'modes_de_transport' => 'modes_de_transport',
          'domaines_d_activites' => 'domaines_d_activites',
        ];

        if (isset($vocabulary_mapping[$vocabulary])) {
          $mapped_vocabulary = $vocabulary_mapping[$vocabulary];
          $logger->notice('Utilisation du vocabulaire @mapped au lieu de @original', [
            '@mapped' => $mapped_vocabulary,
            '@original' => $vocabulary,
          ]);
          $vocabulary = $mapped_vocabulary;

          // Vérifier si le vocabulaire mappé existe
          $vocabularies = $this->entityTypeManager
            ->getStorage('taxonomy_vocabulary')
            ->loadByProperties(['vid' => $vocabulary]);

          if (empty($vocabularies)) {
            $logger->error('Le vocabulaire mappé @vocabulary n\'existe pas non plus.', [
              '@vocabulary' => $vocabulary,
            ]);
            return null;
          }
        } else {
          $logger->error('Impossible de mapper le vocabulaire @vocabulary à un vocabulaire existant.', [
            '@vocabulary' => $vocabulary,
          ]);
          return null;
        }
      }

      // Rechercher le terme existant
      $terms = $this->entityTypeManager
        ->getStorage('taxonomy_term')
        ->loadByProperties(['name' => $name, 'vid' => $vocabulary]);

      if (!empty($terms)) {
        $term = reset($terms);
        $term_id = $term->id();

        // Cache the result
        $this->termCache[$cache_key] = $term_id;

        // Si on a un nom en arabe et que le module de traduction de contenu est activé,
        // on ajoute ou met à jour la traduction arabe du terme
        if (!empty($name_ar) && \Drupal::moduleHandler()->moduleExists('content_translation')) {
          $this->addTermTranslation($term, $name_ar);
        }

        return $term_id;
      }

      // Créer un nouveau terme en français
      $term = Term::create([
        'name' => $name,
        'vid' => $vocabulary,
        'langcode' => 'fr',
      ]);
      $term->save();
      $term_id = $term->id();

      // Cache the result
      $this->termCache[$cache_key] = $term_id;

      // Si on a un nom en arabe et que le module de traduction de contenu est activé,
      // on ajoute la traduction arabe du terme
      if (!empty($name_ar) && \Drupal::moduleHandler()->moduleExists('content_translation')) {
        $this->addTermTranslation($term, $name_ar);
      }

      $logger->notice('Terme créé: @name dans le vocabulaire @vocabulary avec ID @id', [
        '@name' => $name,
        '@vocabulary' => $vocabulary,
        '@id' => $term_id,
      ]);

      return $term_id;
    }
    catch (\Exception $e) {
      $logger = $this->loggerFactory->get('import_reglementation');
      $logger->error('Erreur lors de la création du terme @name: @error', [
        '@name' => $name,
        '@error' => $e->getMessage(),
      ]);
      return null;
    }
  }

  /**
   * Parses a date string and returns a formatted date.
   *
   * @param string $date_string
   *   The date string to parse.
   *
   * @return string|null
   *   The formatted date (Y-m-d) or null if parsing failed.
   */
  protected function parseDate($date_string) {
    if (empty($date_string)) {
      return null;
    }

    // Sanitize input
    $date_string = $this->sanitizeInput($date_string);
    $logger = $this->loggerFactory->get('import_reglementation');

    // Gérer les formats comme "27-juil.-72", "25-fév-94", "22-jui-01", etc.
    if (preg_match('/^(\d{1,2})-([\wéûù]+)\.?-(\d{2})$/', $date_string, $matches)) {
      $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
      $month_text = strtolower($matches[2]);
      $year = $matches[3];

      // Mapping des mois en français vers leur numéro
      $month_mapping = [
        'janv' => '01', 'jan' => '01', 'janvier' => '01',
        'févr' => '02', 'fév' => '02', 'février' => '02', 'fevr' => '02', 'fev' => '02',
        'mars' => '03', 'mar' => '03',
        'avr' => '04', 'avril' => '04',
        'mai' => '05',
        'juin' => '06', 'jui' => '06',
        'juil' => '07', 'juillet' => '07', 'jul' => '07',
        'août' => '08', 'aout' => '08', 'aoû' => '08', 'aou' => '08',
        'sept' => '09', 'sep' => '09', 'septembre' => '09',
        'oct' => '10', 'octobre' => '10',
        'nov' => '11', 'novembre' => '11',
        'déc' => '12', 'dec' => '12', 'décembre' => '12', 'decembre' => '12'
      ];

      // Trouver le numéro du mois
      $month = '01'; // Valeur par défaut
      foreach ($month_mapping as $abbr => $num) {
        if (strpos($month_text, $abbr) === 0) {
          $month = $num;
          break;
        }
      }

      // Déterminer l'année complète (sécurité: limiter à une plage raisonnable)
      $year_int = (int) $year;
      if ($year_int > 50 && $year_int <= 99) {
        $year_full = "19{$year}";
      } elseif ($year_int >= 0 && $year_int <= 50) {
        $year_full = "20{$year}";
      } else {
        $logger->warning('Année invalide dans la date: @date', ['@date' => $date_string]);
        return null;
      }

      // Créer la date au format Y-m-d
      $formatted_date = "{$year_full}-{$month}-{$day}";

      // Valider la date
      try {
        $date_object = DrupalDateTime::createFromFormat('Y-m-d', $formatted_date);
        if ($date_object && !array_sum($date_object->getErrors())) {
          return $formatted_date;
        }
      } catch (\Exception $e) {
        $logger->warning('Erreur lors de la validation de la date: @error', ['@error' => $e->getMessage()]);
      }
    }

    // Formats standards à essayer
    $formats = ['d.m.Y', 'd/m/Y', 'Y-m-d', 'd-m-Y'];
    foreach ($formats as $format) {
      try {
        $date_attempt = DrupalDateTime::createFromFormat($format, $date_string);
        if ($date_attempt && !array_sum($date_attempt->getErrors())) {
          return $date_attempt->format('Y-m-d');
        }
      } catch (\Exception $e) {
        // Continue to next format
      }
    }

    $logger->warning('Format de date non reconnu: @date', ['@date' => $date_string]);
    return null;
  }

  /**
   * Ajoute une traduction en arabe pour un terme de taxonomie.
   *
   * @param \Drupal\taxonomy\Entity\Term $term
   *   Le terme à traduire.
   * @param string $name_ar
   *   Le nom du terme en arabe.
   */
  protected function addTermTranslation(Term $term, $name_ar) {
    try {
      // Sanitize input
      $name_ar = $this->sanitizeInput($name_ar);

      if (empty($name_ar)) {
        return;
      }

      $logger = $this->loggerFactory->get('import_reglementation');

      // Vérifier si le terme a déjà une traduction en arabe
      if ($term->hasTranslation('ar')) {
        $translation = $term->getTranslation('ar');
      }
      else {
        // Créer une nouvelle traduction
        $translation = $term->addTranslation('ar');
      }

      // Définir le nom en arabe
      $translation->setName($name_ar);

      // Sauvegarder la traduction
      $translation->save();

      $logger->notice('Traduction arabe ajoutée pour le terme ID @id', [
        '@id' => $term->id(),
      ]);
    }
    catch (\Exception $e) {
      $logger = $this->loggerFactory->get('import_reglementation');
      $logger->error('Erreur lors de l\'ajout de la traduction arabe pour le terme ID @id: @error', [
        '@id' => $term->id(),
        '@error' => $e->getMessage(),
      ]);
    }
  }
}